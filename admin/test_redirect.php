<?php
require_once 'includes/header.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log("Test redirect form submitted");
    
    if (isset($_POST['test_success'])) {
        error_log("Setting success message and redirecting");
        Utilities::setFlashMessage('success', 'Test redirect successful!');
        Utilities::redirect('users.php');
        exit;
    }
    
    if (isset($_POST['test_error'])) {
        error_log("Setting error message");
        Utilities::setFlashMessage('danger', 'Test error message!');
    }
}
?>

<div class="container-fluid px-4 py-3">
    <h2>Test Redirect</h2>
    
    <?php Utilities::displayFlashMessages(); ?>
    
    <form method="post" action="test_redirect.php">
        <button type="submit" name="test_success" value="1" class="btn btn-success">
            Test Success Redirect
        </button>
        <button type="submit" name="test_error" value="1" class="btn btn-danger">
            Test Error Message
        </button>
    </form>
</div>

<?php require_once 'includes/footer.php'; ?>
