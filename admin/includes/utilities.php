<?php
class Utilities {
    // Redirect to a URL
    public static function redirect($url) {
        // Check if headers have already been sent
        if (headers_sent()) {
            // Use JavaScript for redirection
            echo '<script>window.location.href="' . $url . '";</script>';
            echo '<noscript><meta http-equiv="refresh" content="0;url=' . $url . '"></noscript>';
            exit;
        } else {
            // Clean any output buffers
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Perform the redirect
            header("Location: $url");
            exit;
        }
    }

    // Display flash message
    public static function setFlashMessage($type, $message, $persistent = false) {
        $_SESSION['flash_message'] = [
            'type' => $type,
            'message' => $message,
            'persistent' => $persistent
        ];
    }

    // Get and clear flash message
    public static function getFlashMessage() {
        if (isset($_SESSION['flash_message'])) {
            $message = $_SESSION['flash_message'];
            unset($_SESSION['flash_message']);
            // Filter out 'Welcome back, ...' messages
            if (isset($message['message']) && preg_match('/^Welcome back, .*!$/', $message['message'])) {
                return null;
            }
            return $message;
        }
        return null;
    }

    // Display flash messages
    public static function displayFlashMessages() {
        $message = self::getFlashMessage();
        if ($message) {
            $type = $message['type'];
            $text = $message['message'];
            $persistent = isset($message['persistent']) ? $message['persistent'] : false;

            // Map message type to Bootstrap alert class and icon
            $alertClass = 'alert-info';
            $icon = 'fa-info-circle';

            if ($type === 'success') {
                $alertClass = 'alert-success';
                $icon = 'fa-check-circle';
            } elseif ($type === 'error' || $type === 'danger') {
                $alertClass = 'alert-danger';
                $icon = 'fa-exclamation-circle';
            } elseif ($type === 'warning') {
                $alertClass = 'alert-warning';
                $icon = 'fa-exclamation-triangle';
            }

            // Add persistent class if needed
            $persistentClass = $persistent ? ' alert-persistent' : '';

            echo '<div class="alert ' . $alertClass . $persistentClass . ' alert-dismissible fade show d-flex align-items-center" role="alert">';
            echo '<div class="me-3"><i class="fas ' . $icon . ' fa-lg"></i></div>';
            echo '<div class="flex-grow-1">' . $text . '</div>';
            echo '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
            echo '</div>';
        }
    }

    // Sanitize input
    public static function sanitizeInput($input) {
        if (is_array($input)) {
            foreach ($input as $key => $value) {
                $input[$key] = self::sanitizeInput($value);
            }
        } else {
            $input = trim($input);
            $input = stripslashes($input);
            $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
        }
        return $input;
    }

    // Format date
    public static function formatDate($date, $format = 'M d, Y') {
        // Check if date is null or empty
        if ($date === null || $date === '') {
            return 'N/A';
        }

        // Convert to timestamp and format
        $timestamp = strtotime($date);
        if ($timestamp === false) {
            return 'Invalid Date';
        }

        return date($format, $timestamp);
    }

    /**
     * Safely convert a date string to a timestamp
     *
     * @param string|null $date The date string to convert
     * @param mixed $default The default value to return if the date is invalid (default: false)
     * @return int|bool The timestamp or the default value
     */
    public static function safeStrtotime($date, $default = false) {
        if ($date === null || $date === '') {
            return $default;
        }

        $timestamp = strtotime($date);
        if ($timestamp === false) {
            return $default;
        }

        return $timestamp;
    }

    // Generate random string
    public static function generateRandomString($length = 10) {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $randomString = '';

        for ($i = 0; $i < $length; $i++) {
            $randomString .= $characters[rand(0, strlen($characters) - 1)];
        }

        return $randomString;
    }

    // Format file size
    public static function formatFileSize($bytes) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= (1 << (10 * $pow));

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    // Validate email
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL);
    }

    // Truncate text to a specified length
    public static function truncateText($text, $length = 100, $append = '...') {
        if (strlen($text) <= $length) {
            return $text;
        }

        $text = substr($text, 0, $length);
        $text = substr($text, 0, strrpos($text, ' '));

        return $text . $append;
    }

    // Get client IP address
    public static function getClientIP() {
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            return $_SERVER['HTTP_X_FORWARDED_FOR'];
        } else {
            return $_SERVER['REMOTE_ADDR'];
        }
    }

    // Generate a consistent color from a name
    public static function getColorFromName($name) {
        // Generate a hash from the name
        $hash = md5($name);

        // Use the first 6 characters of the hash as a hex color
        $color = '#' . substr($hash, 0, 6);

        // Predefined colors that look good for avatars
        $colors = [
            '#4361ee', // Blue
            '#3a0ca3', // Indigo
            '#7209b7', // Purple
            '#f72585', // Pink
            '#4cc9f0', // Cyan
            '#4895ef', // Light Blue
            '#560bad', // Violet
            '#f3722c', // Orange
            '#f8961e', // Light Orange
            '#43aa8b', // Teal
            '#277da1', // Dark Blue
            '#577590', // Steel Blue
        ];

        // Use the hash to select a color from the predefined list
        $index = hexdec(substr($hash, 0, 8)) % count($colors);
        return $colors[$index];
    }

    // Validate date string against a format
    public static function validateDate($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) === $date;
    }

    public static function generateAuthToken($userId) {
        // Simple token: user ID + random string + timestamp (for demo only, not secure)
        return base64_encode($userId . ':' . bin2hex(random_bytes(16)) . ':' . time());
    }

    public static function notifyWebSocketClients($data) {
        $url = 'http://192.168.1.11:8080/notify';
        $options = [
            'http' => [
                'header'  => "Content-type: application/json\r\n",
                'method'  => 'POST',
                'content' => json_encode($data),
                'timeout' => 2
            ]
        ];
        $context  = stream_context_create($options);
        @file_get_contents($url, false, $context);
    }

    /**
     * Convert a timestamp to a human-readable time ago string
     *
     * @param string|int $timestamp The timestamp or date string to convert
     * @return string Human-readable time difference (e.g., "2 hours ago")
     */
    public static function timeAgo($timestamp) {
        if (!is_numeric($timestamp)) {
            $timestamp = strtotime($timestamp);
        }

        if ($timestamp === false) {
            return 'Invalid date';
        }

        $current_time = time();
        $diff = $current_time - $timestamp;

        // Define time intervals in seconds
        $intervals = [
            31536000 => 'year',
            2592000 => 'month',
            604800 => 'week',
            86400 => 'day',
            3600 => 'hour',
            60 => 'minute',
            1 => 'second'
        ];

        // Calculate the time difference
        foreach ($intervals as $seconds => $label) {
            $count = floor($diff / $seconds);

            if ($count > 0) {
                if ($count == 1) {
                    return "1 $label ago";
                } else {
                    return "$count {$label}s ago";
                }
            }
        }

        return 'Just now';
    }

    // CSRF token generation
    public static function generateCsrfToken() {
        if (empty($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    // CSRF token validation
    public static function validateCsrfToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * Normalize phone number for consistent matching
     * Removes all non-digit characters and handles various international formats
     *
     * @param string $phone The phone number to normalize
     * @return string The normalized phone number (digits only)
     */
    public static function normalizePhoneNumber($phone) {
        if (empty($phone)) {
            return '';
        }

        // Remove all non-digit characters
        $digits = preg_replace('/[^0-9]/', '', $phone);

        // Handle common country code patterns
        // If starts with 91 and has 12 digits total (91 + 10 digit number), keep as is
        if (strlen($digits) === 12 && substr($digits, 0, 2) === '91') {
            return $digits;
        }

        // If starts with 1 and has 11 digits total (1 + 10 digit number), keep as is
        if (strlen($digits) === 11 && substr($digits, 0, 1) === '1') {
            return $digits;
        }

        // If exactly 10 digits, assume it's a local number without country code
        if (strlen($digits) === 10) {
            return $digits;
        }

        // For other lengths, return as is (could be other country codes)
        return $digits;
    }

    /**
     * Generate possible phone number variations for matching
     * Returns an array of possible formats to check against database
     *
     * @param string $phone The input phone number
     * @return array Array of possible phone number formats
     */
    public static function getPhoneNumberVariations($phone) {
        $normalized = self::normalizePhoneNumber($phone);
        $variations = [];

        if (empty($normalized)) {
            return $variations;
        }

        // Add the normalized version
        $variations[] = $normalized;

        // If it's 10 digits, add with +91 prefix (for Indian numbers)
        if (strlen($normalized) === 10) {
            $variations[] = '91' . $normalized;
            $variations[] = '+91' . $normalized;
        }

        // If it starts with 91 and has 12 digits, also add without country code
        if (strlen($normalized) === 12 && substr($normalized, 0, 2) === '91') {
            $variations[] = substr($normalized, 2);
            $variations[] = '+' . $normalized;
        }

        // If it starts with 1 and has 11 digits, also add without country code
        if (strlen($normalized) === 11 && substr($normalized, 0, 1) === '1') {
            $variations[] = substr($normalized, 1);
            $variations[] = '+' . $normalized;
        }

        // Add original input if it's different from normalized
        if ($phone !== $normalized) {
            $variations[] = $phone;
        }

        // Remove duplicates and return
        return array_unique($variations);
    }
}
