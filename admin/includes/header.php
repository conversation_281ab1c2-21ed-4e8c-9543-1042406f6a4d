<?php
// Start output buffering
ob_start();

require_once 'config.php';
require_once 'database.php';
require_once 'auth.php';
require_once 'utilities.php';
require_once 'permissions.php';

// Initialize auth
$auth = new Auth();

// CSRF token generation
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Check if user is logged in
if (!$auth->isLoggedIn() && basename($_SERVER['PHP_SELF']) !== 'login.php') {
    Utilities::redirect('login.php');
}

// Check session timeout
if (!$auth->checkSessionTimeout() && basename($_SERVER['PHP_SELF']) !== 'login.php') {
    Utilities::setFlashMessage('warning', 'Your session has expired. Please log in again.');
    Utilities::redirect('login.php');
}

// Skip permission check for login, logout, unauthorized pages
$noCheckPages = ['login.php', 'logout.php', 'unauthorized.php', 'index.php'];
$currentPage = basename($_SERVER['PHP_SELF']);

// Check if the current page requires permission
if (!in_array($currentPage, $noCheckPages)) {
    // Get the required permission for the current page
    $requiredPermission = getRequiredPermissionForCurrentPage();

    // If the page requires permission, check if the user has it
    if ($requiredPermission !== null) {
        // For staff members, check specific permissions
        if ($_SESSION['role'] === 'staff') {
            if (!hasPermission($requiredPermission)) {
                redirectToUnauthorized();
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Google Fonts - Inter -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">

    <!-- Modern CSS -->
    <link rel="stylesheet" href="assets/css/modern.css">

    <!-- Admin Table CSS -->
    <link rel="stylesheet" href="assets/css/admin-table.css">

    <!-- Global Dropdown Fix CSS -->
    <link rel="stylesheet" href="assets/css/global-dropdown-fix.css">

    <!-- Responsive Admin Panel CSS -->
    <link rel="stylesheet" href="assets/css/responsive-admin-panel.css">

    <!-- PWA Install CSS -->
    <link rel="stylesheet" href="assets/css/pwa-install.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>



    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="assets/img/favicon.svg">
    <link rel="alternate icon" href="assets/img/favicon.ico" type="image/x-icon">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#27ae60">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="KFT Admin">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="application-name" content="KFT Admin">

    <!-- PWA Manifest -->
    <link rel="manifest" href="manifest.json">

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" sizes="72x72" href="assets/img/pwa-icon-72x72.png">
    <link rel="apple-touch-icon" sizes="96x96" href="assets/img/pwa-icon-96x96.png">
    <link rel="apple-touch-icon" sizes="128x128" href="assets/img/pwa-icon-128x128.png">
    <link rel="apple-touch-icon" sizes="144x144" href="assets/img/pwa-icon-144x144.png">
    <link rel="apple-touch-icon" sizes="152x152" href="assets/img/pwa-icon-152x152.png">
    <link rel="apple-touch-icon" sizes="192x192" href="assets/img/pwa-icon-192x192.png">

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#27ae60">
    <meta name="msapplication-TileImage" content="assets/img/pwa-icon-144x144.png">
    <meta name="msapplication-config" content="browserconfig.xml">

    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
    </style>

    <!-- PWA Service Worker Registration -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }
    </script>
</head>
<body>
<?php Utilities::displayFlashMessages(); ?>

<div class="admin-layout" id="adminLayout">
    <!-- Sidebar Navigation -->
    <div class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-dumbbell me-2"></i>
                <span class="brand-text">KFT Admin</span>
            </div>
            <button class="sidebar-close d-lg-none" id="sidebarClose" aria-label="Close navigation">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="index.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'index.php') ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt nav-icon"></i>
                        <span class="nav-text">Overview</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="users.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'users.php') ? 'active' : ''; ?>">
                        <i class="fas fa-users nav-icon"></i>
                        <span class="nav-text">Users</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="courses.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'courses.php') ? 'active' : ''; ?>">
                        <i class="fas fa-graduation-cap nav-icon"></i>
                        <span class="nav-text">Courses</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="staff_management.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'staff_management.php') ? 'active' : ''; ?>">
                        <i class="fas fa-users-cog nav-icon"></i>
                        <span class="nav-text">Staff</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="settings.php" class="nav-link <?php echo (basename($_SERVER['PHP_SELF']) == 'settings.php') ? 'active' : ''; ?>">
                        <i class="fas fa-cogs nav-icon"></i>
                        <span class="nav-text">Settings</span>
                    </a>
                </li>
                <li class="nav-item nav-divider">
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt nav-icon"></i>
                        <span class="nav-text">Sign Out</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Main Content Area -->
    <div class="admin-content" id="adminContent">
        <!-- Top Navigation Bar -->
        <div class="admin-topbar">
            <div class="topbar-left">
                <button class="nav-toggle" id="navToggle" aria-label="Toggle navigation">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
            </div>
            <div class="topbar-user">
                <div class="user-avatar" id="userAvatarDropdown" style="cursor: pointer;">
                    <?php echo strtoupper(substr($_SESSION['name'] ?? $_SESSION['username'] ?? 'U', 0, 1)); ?>
                </div>
                <span class="d-none d-md-inline">
                    <?php echo htmlspecialchars($_SESSION['name'] ?? $_SESSION['username'] ?? 'User'); ?>
                </span>
            </div>
        </div>
        <div class="user-dropdown-menu" id="userDropdownMenu" style="display: none; position: absolute; right: 1rem; top: 3.5rem; background: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 8px; z-index: 1000;">
            <a href="profile.php" class="user-dropdown-item">Profile</a>
            <a href="logout.php" class="user-dropdown-item">Sign Out</a>
        </div>
        <script>
            document.getElementById('userAvatarDropdown').addEventListener('click', function() {
                var dropdown = document.getElementById('userDropdownMenu');
                dropdown.style.display = dropdown.style.display === 'none' ? 'block' : 'none';
            });
            document.addEventListener('click', function(event) {
                var dropdown = document.getElementById('userDropdownMenu');
                var avatar = document.getElementById('userAvatarDropdown');
                if (!avatar.contains(event.target) && !dropdown.contains(event.target)) {
                    dropdown.style.display = 'none';
                }
            });
        </script>

        <!-- Page Content Container -->
        <div class="container-fluid px-4 py-3">
