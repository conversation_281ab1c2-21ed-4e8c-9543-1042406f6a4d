<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// --- API (App) Login Handler ---
if (
    $_SERVER['REQUEST_METHOD'] === 'POST' &&
    (isset($_SERVER['HTTP_CONTENT_TYPE']) && strpos($_SERVER['HTTP_CONTENT_TYPE'], 'application/json') !== false)
) {
    // Read JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    $phone = $input['phone_number'] ?? '';
    $pin = $input['pin'] ?? '';
    $device_id = $input['device_id'] ?? '';

    // Validate
    if (empty($phone) || empty($pin)) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => 'Phone number and PIN required.']);
        exit;
    }

    // Lookup user by phone (normalize input)
    $normalizedPhone = preg_replace('/[^0-9]/', '', $phone);
    $withCountry = (strlen($normalizedPhone) === 10) ? '+91' . $normalizedPhone : $normalizedPhone;
    error_log("DEBUG: Login attempt for phone: $phone, normalized: $normalizedPhone, withCountry: $withCountry, PIN: $pin, device_id: $device_id");
    $db = new Database();
    $user = $db->getUserByPhone($phone);
    if ($user) {
        error_log("DEBUG: DB user found: id={$user['id']}, phone={$user['phone']}, phone_number={$user['phone_number']}, pin={$user['pin']}, pin_expires_at={$user['pin_expires_at']}, device_id={$user['device_id']}");
    }

    if (!$user) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => 'Phone number not found.']);
        exit;
    }

    // Compare PINs and log
    error_log("DEBUG: Comparing entered PIN '$pin' with DB PIN '{$user['pin']}'");
    if ($user['pin'] === $pin) {
        // Check if PIN is single-use and already used
        if (isset($user['pin_used']) && $user['pin_used'] == 1) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'PIN already used. Please contact admin.']);
            exit;
        }
        // Check PIN expiry if pin_expires_at is set
        if (!empty($user['pin_expires_at']) && strtotime($user['pin_expires_at']) < time()) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'PIN expired. Please contact admin.']);
            exit;
        }
        // Device restriction logic
        if (empty($user['device_id'])) {
            // First login: save device_id
            $db->updateUserDeviceId($user['id'], $device_id);
            $user['device_id'] = $device_id; // Update local variable for consistency
        } elseif ($user['device_id'] !== $device_id) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'error' => 'Already logged in on another device. Please contact admin.']);
            exit;
        }
        // Update last_login timestamp
        $db->updateUserLastLogin($user['id']);
        // Mark PIN as used if single-use
        if (isset($user['pin_used']) && $user['pin_used'] == 0) {
            $db->getConnection()->query("UPDATE users SET pin_used = 1 WHERE id = " . intval($user['id']));
        }
        // --- JWT token generation ---
        require_once 'includes/jwt.php';

        // Determine token expiration based on request
        $extendedSession = isset($_POST['extended_session']) && $_POST['extended_session'] === true;
        $rememberMe = isset($_POST['remember_me']) && $_POST['remember_me'] === true;

        // Set token expiration
        if ($extendedSession || $rememberMe) {
            $tokenExpiry = time() + (300 * 24 * 60 * 60); // 300 days for persistent login
        } else {
            $tokenExpiry = time() + (7 * 24 * 60 * 60); // 7 days for regular login
        }

        $jwtPayload = [
            'user_id' => $user['id'],
            'name' => $user['name'],
            'iat' => time(),
            'exp' => $tokenExpiry,
            'extended' => $extendedSession || $rememberMe
        ];

        $token = generate_jwt($jwtPayload, APP_SECRET);

        // Enhanced logging in development mode
        if (defined('DEV_MODE') && DEV_MODE === true) {
            error_log('=== JWT TOKEN GENERATION (login.php) ===');
            error_log('User ID: ' . $user['id']);
            error_log('User Name: ' . $user['name']);
            error_log('Current time: ' . time() . ' (' . date('Y-m-d H:i:s') . ')');
            error_log('Expiry time: ' . ($jwtPayload['exp']) . ' (' . date('Y-m-d H:i:s', $jwtPayload['exp']) . ')');
            error_log('Time until expiry: ' . ($jwtPayload['exp'] - time()) . ' seconds');
            error_log('Generated JWT token (first 50 chars): ' . substr($token, 0, 50) . '...');
            error_log('=== END JWT TOKEN GENERATION ===');
        }
        // Generate refresh token for extended sessions
        $refreshToken = null;
        if ($extendedSession || $rememberMe) {
            $refreshTokenPayload = [
                'user_id' => $user['id'],
                'type' => 'refresh',
                'iat' => time(),
                'exp' => time() + (365 * 24 * 60 * 60) // 1 year for refresh token
            ];
            $refreshToken = generate_jwt($refreshTokenPayload, APP_SECRET . '_refresh');
        }

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'user_id' => $user['id'],
            'name' => $user['name'],
            'token' => $token,
            'refresh_token' => $refreshToken,
            'expires_at' => date('Y-m-d H:i:s', $tokenExpiry),
            'extended_session' => $extendedSession || $rememberMe
        ]);
        exit;
    } else {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'error' => 'Invalid PIN.']);
        exit;
    }
}

// Initialize auth
$auth = new Auth();

// Check if user is already logged in
if ($auth->isLoggedIn()) {
    Utilities::redirect('index.php');
}

// Enforce HTTPS
// if (empty($_SERVER['HTTPS']) || $_SERVER['HTTPS'] === 'off') {
//     $redirect = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
//     header('Location: ' . $redirect);
//     exit;
// }

// CSRF token for login form
$csrfToken = Utilities::generateCsrfToken();

// Process login form
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Rate limiting and account lockout logic
    $maxAttempts = 5;
    $lockoutTime = 15 * 60; // 15 minutes
    $ip = $_SERVER['REMOTE_ADDR'];
    if (!isset($_SESSION['login_attempts'])) {
        $_SESSION['login_attempts'] = [];
    }
    if (!isset($_SESSION['lockout'])) {
        $_SESSION['lockout'] = [];
    }
    // Check lockout
    if (isset($_SESSION['lockout'][$ip]) && $_SESSION['lockout'][$ip] > time()) {
        $error = 'Too many failed attempts. Try again in ' . ceil(($_SESSION['lockout'][$ip] - time()) / 60) . ' minutes.';
    } else {
        // Validate CSRF token
        if (!isset($_POST['csrf_token']) || !Utilities::validateCsrfToken($_POST['csrf_token'])) {
            $error = 'Invalid CSRF token.';
        } else {
            // Sanitize inputs
            $username = Utilities::sanitizeInput($_POST['username'] ?? '');
            $password = $_POST['password'] ?? '';

            // Validate inputs
            if (empty($username) || empty($password)) {
                $error = 'Please enter both username and password.';
            } else {
                // Debug info
                error_log("Login attempt: Username: $username, Password: $password");

                // Attempt to login
                if ($auth->login($username, $password)) {
                    error_log("Login successful for user: $username");
                    Utilities::redirect('index.php');
                } else {
                    error_log("Login failed for user: $username");
                    // Increment attempts
                    if (!isset($_SESSION['login_attempts'][$ip])) {
                        $_SESSION['login_attempts'][$ip] = 0;
                    }
                    $_SESSION['login_attempts'][$ip]++;
                    if ($_SESSION['login_attempts'][$ip] >= $maxAttempts) {
                        $_SESSION['lockout'][$ip] = time() + $lockoutTime;
                        $error = 'Too many failed attempts. Try again in 15 minutes.';
                    } else {
                        $error = 'Invalid username or password.';
                    }
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - <?php echo APP_NAME; ?></title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">

    <!-- Google Fonts - Inter -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">

    <!-- Modern CSS -->
    <link rel="stylesheet" href="assets/css/modern.css">
</head>
<body class="bg-light">
    <div class="container">
        <div class="login-container">
            <div class="login-logo">
                <h2 class="text-primary font-weight-bold">KFT Fitness</h2>
                <p class="text-muted">Admin Dashboard</p>
            </div>

            <h4 class="login-title">Sign In</h4>

            <?php if (!empty($error)): ?>
            <div class="alert alert-danger d-flex align-items-center" role="alert">
                <div class="me-3"><i class="fas fa-exclamation-circle fa-lg"></i></div>
                <div><?php echo $error; ?></div>
            </div>
            <?php endif; ?>

            <?php
            $flashMessage = Utilities::getFlashMessage();
            if ($flashMessage):
                $icon = 'fa-info-circle';
                if ($flashMessage['type'] === 'success') {
                    $icon = 'fa-check-circle';
                } elseif ($flashMessage['type'] === 'error' || $flashMessage['type'] === 'danger') {
                    $icon = 'fa-exclamation-circle';
                } elseif ($flashMessage['type'] === 'warning') {
                    $icon = 'fa-exclamation-triangle';
                }
            ?>
            <div class="alert alert-<?php echo $flashMessage['type']; ?> d-flex align-items-center" role="alert">
                <div class="me-3"><i class="fas <?php echo $icon; ?> fa-lg"></i></div>
                <div><?php echo $flashMessage['message']; ?></div>
            </div>
            <?php endif; ?>

            <form method="post" action="login.php">
                <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrfToken); ?>">
                <div class="mb-4">
                    <label for="username" class="form-label">Phone Number or Username <span class='text-muted' style='font-size:0.95em;'>(any format accepted)</span></label>
                    <div class="input-group">
                        <span class="input-group-text bg-light border-end-0"><i class="fas fa-user text-muted"></i></span>
                        <input type="text" class="form-control border-start-0" id="username" name="username" placeholder="e.g., +919876543210, 9876543210, or username" inputmode="tel" autocomplete="username" required>
                    </div>
                    <div class="form-text">Enter phone number with or without country code, or your username</div>
                </div>

                <div class="mb-4">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-group-text bg-light border-end-0"><i class="fas fa-lock text-muted"></i></span>
                        <input type="password" class="form-control border-start-0" id="password" name="password" placeholder="Enter your password" required>
                        <button class="btn btn-outline-secondary" type="button" id="togglePassword" tabindex="-1" style="border-top-left-radius: 0; border-bottom-left-radius: 0;">
                            <i class="fas fa-eye" id="togglePasswordIcon"></i>
                        </button>
                    </div>
                    <!-- Removed the 'Reveal password' text link -->
                </div>

                <div class="mb-4 d-flex justify-content-between align-items-center">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                        <label class="form-check-label" for="remember">Remember me</label>
                    </div>
                    <a href="forgot_password.php" class="text-decoration-none text-primary">Forgot password?</a>
                </div>

                <div class="d-grid gap-2 mb-4">
                    <button type="submit" class="btn btn-primary py-2">Sign In</button>
                </div>
            </form>

        </div>

        <div class="text-center mt-4 text-muted">
            <small>&copy; <?php echo date('Y'); ?> <?php echo APP_NAME; ?>. All rights reserved.</small>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.getElementById('password');
    const togglePasswordBtn = document.getElementById('togglePassword');
    const togglePasswordIcon = document.getElementById('togglePasswordIcon');
    const revealPasswordText = document.getElementById('revealPasswordText');

    function togglePasswordVisibility() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);
        togglePasswordIcon.classList.toggle('fa-eye');
        togglePasswordIcon.classList.toggle('fa-eye-slash');
        revealPasswordText.textContent = type === 'password' ? 'Reveal password' : 'Hide password';
    }

    togglePasswordBtn.addEventListener('click', function(e) {
        e.preventDefault();
        togglePasswordVisibility();
    });
    revealPasswordText.addEventListener('click', function(e) {
        e.preventDefault();
        togglePasswordVisibility();
    });
});
</script>
</body>
</html><?php // End of file ?>
