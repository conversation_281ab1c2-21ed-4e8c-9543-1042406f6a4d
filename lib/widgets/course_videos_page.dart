import 'package:flutter/material.dart';
import '../models/course.dart';
import '../models/course_video.dart';
import '../models/user_profile.dart';
import '../services/api_service.dart';
import '../pages/video_player_page.dart';
import '../pages/dedicated_video_player_page.dart';
import '../pages/comprehensive_video_player_page.dart';
import '../design_system/kft_design_system.dart';
import '../utils/video_overlay_helper.dart';
import 'package:provider/provider.dart';
import '../services/progress_service.dart';

class CourseVideosPage extends StatefulWidget {
  final int courseId;

  const CourseVideosPage({Key? key, required this.courseId}) : super(key: key);

  @override
  _CourseVideosPageState createState() => _CourseVideosPageState();
}

class _CourseVideosPageState extends State<CourseVideosPage> {
  final ApiService _apiService = ApiService();
  bool _isLoading = true;
  Map<String, dynamic>? _courseData;
  List<CourseVideo> _videos = [];
  Map<String, List<CourseVideo>> _videosByWeek = {};
  String _errorMessage = '';
  bool isLowEndDevice = false; // Assuming this is a global variable

  @override
  void initState() {
    super.initState();
    _loadCourseVideos();
  }

  Future<void> _loadCourseVideos() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      final data = await _apiService.getCourseVideos(widget.courseId);
      setState(() {
        // Handle course data safely - it could be a Map, List, or null
        if (data['course'] is Map<String, dynamic>) {
          _courseData = data['course'] as Map<String, dynamic>;
        } else if (data['course'] is List) {
          // If course is a List, create a default Map
          _courseData = {
            'id': widget.courseId,
            'title': 'Course Videos',
            'description': '',
            'thumbnail_url': '',
            'duration_weeks': 1,
            'total_videos': 0,
            'completed_videos': 0,
            'progress_percentage': 0
          };
        } else if (data['course'] == null) {
          // If course is null, create a default Map
          _courseData = {
            'id': widget.courseId,
            'title': 'Course Videos',
            'description': '',
            'thumbnail_url': '',
            'duration_weeks': 1,
            'total_videos': 0,
            'completed_videos': 0,
            'progress_percentage': 0
          };
        }

        // Handle videos safely - parse from JSON to CourseVideo objects
        if (data['videos'] is List) {
          final videosData = data['videos'] as List;
          _videos = <CourseVideo>[];

          for (var videoData in videosData) {
            try {
              if (videoData is Map<String, dynamic>) {
                _videos.add(CourseVideo.fromJson(videoData));
              } else if (videoData is Map) {
                _videos.add(CourseVideo.fromJson(Map<String, dynamic>.from(videoData)));
              }
            } catch (e) {
              print('Error parsing course video: $e');
              // Skip invalid video data instead of failing completely
            }
          }

          // Update course data with video count
          if (_courseData != null) {
            _courseData!['total_videos'] = _videos.length;
            _courseData!['completed_videos'] = _videos.where((v) => v.isCompleted).length;
            if (_videos.isNotEmpty) {
              _courseData!['progress_percentage'] =
                (_videos.where((v) => v.isCompleted).length / _videos.length * 100).round();
            }
          }
        } else {
          _videos = [];
        }

        // Handle videos_by_week safely - it could be a Map or a List
        if (data['videos_by_week'] is Map<String, dynamic>) {
          try {
            _videosByWeek = Map<String, List<CourseVideo>>.from(data['videos_by_week']);
          } catch (e) {
            // If conversion fails, organize videos by week manually
            _videosByWeek = {};
            for (var video in _videos) {
              final weekKey = video.weekNumber.toString();
              if (!_videosByWeek.containsKey(weekKey)) {
                _videosByWeek[weekKey] = [];
              }
              _videosByWeek[weekKey]!.add(video);
            }
          }
        } else {
          // If it's not a Map, create an empty Map and organize videos by week manually
          _videosByWeek = {};
          for (var video in _videos) {
            final weekKey = video.weekNumber.toString();
            if (!_videosByWeek.containsKey(weekKey)) {
              _videosByWeek[weekKey] = [];
            }
            _videosByWeek[weekKey]?.add(video);
          }
        }

        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceAll('Exception: ', '');
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Helper function to safely get string values
    String safeString(dynamic value, String defaultValue) {
      if (value == null) return defaultValue;
      return value.toString();
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(_courseData != null && _courseData!.containsKey('title')
            ? safeString(_courseData!['title'], 'Course Videos')
            : 'Course Videos'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCourseVideos,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Error: $_errorMessage',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadCourseVideos,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_videos.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.videocam_off,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            const Text(
              'No videos available for this course',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadCourseVideos,
              child: const Text('Refresh'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadCourseVideos,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Course header
          if (_courseData != null) _buildCourseHeader(),

          const SizedBox(height: 24),

          // Videos by week
          ..._buildVideosByWeek(),
        ],
      ),
    );
  }

  Widget _buildCourseHeader() {
    // Helper function to safely get string values
    String safeString(dynamic value, String defaultValue) {
      if (value == null) return defaultValue;
      return value.toString();
    }

    // Helper function to safely get int values
    int safeInt(dynamic value, int defaultValue) {
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is String) return int.tryParse(value) ?? defaultValue;
      if (value is double) return value.toInt();
      return defaultValue;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Course thumbnail
        if (_courseData != null && _courseData!.containsKey('thumbnail_url') &&
            _courseData!['thumbnail_url'] != null &&
            safeString(_courseData!['thumbnail_url'], '').isNotEmpty)
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: Image.network(
              safeString(_courseData!['thumbnail_url'], ''),
              height: 200,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  height: 200,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: KFTDesignSystem.getCardColor(context),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.image_not_supported,
                    size: 48,
                    color: KFTDesignSystem.getTextSecondaryColor(context),
                  ),
                );
              },
            ),
          )
        else
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(Theme.of(context).brightness == Brightness.dark ? 0.15 : 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.school,
              size: 64,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),

        const SizedBox(height: 16),

        // Course title
        Text(
          safeString(_courseData?['title'] ?? 'Course Videos', 'Course Videos'),
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),

        const SizedBox(height: 8),

        // Course description
        if (_courseData != null && _courseData!.containsKey('description') &&
            _courseData!['description'] != null &&
            safeString(_courseData!['description'], '').isNotEmpty)
                      Text(
              safeString(_courseData!['description'], ''),
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),

        const SizedBox(height: 16),

        // Course duration
        Row(
          children: [
            Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 4),
            Text(
              '${safeInt(_courseData!['duration_weeks'], 1)} weeks',
              style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
            ),
            const SizedBox(width: 16),
            Icon(Icons.video_library, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 4),
            Text(
              '${_videos.length} videos',
              style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
            ),
          ],
        ),
      ],
    );
  }

  List<Widget> _buildVideosByWeek() {
    final List<Widget> weekSections = [];

    // Sort weeks numerically
    final List<String> sortedWeeks = _videosByWeek.keys.toList()
      ..sort((a, b) => int.parse(a).compareTo(int.parse(b)));

    for (final weekKey in sortedWeeks) {
      final weekNumber = int.parse(weekKey);
      final weekVideos = _videosByWeek[weekKey] ?? [];

      // Check if this week is unlocked
      bool isWeekUnlocked = weekVideos.any((video) => video.isUnlocked);
      bool isCurrentWeek = weekVideos.any((video) => video.isCurrentWeek);

      // Get the first locked video to determine when this week will unlock
      CourseVideo? firstLockedVideo;
      try {
        firstLockedVideo = weekVideos.firstWhere(
          (video) => !video.isUnlocked && video.weeksUntilUnlock != null,
          orElse: () => weekVideos.firstWhere(
            (video) => !video.isUnlocked,
            orElse: () => weekVideos.first,
          ),
        );
      } catch (e) {
        // If no videos found, use the first video
        firstLockedVideo = weekVideos.first;
      }

      // Determine week status text and color
      String weekStatus = '';
      Color weekStatusColor = Colors.grey;

      if (isWeekUnlocked) {
        if (weekVideos.every((video) => video.isCompleted)) {
          weekStatus = 'Completed';
          weekStatusColor = Theme.of(context).colorScheme.secondary;
        } else if (isCurrentWeek) {
          weekStatus = 'Current';
          weekStatusColor = Colors.green;
        } else {
          weekStatus = 'Unlocked';
          weekStatusColor = Theme.of(context).colorScheme.primary;
        }
      } else if (isCurrentWeek) {
        weekStatus = 'Soon';
        weekStatusColor = Colors.orange;
      } else if (firstLockedVideo.weeksUntilUnlock == 1) {
        weekStatus = 'Next';
        weekStatusColor = Colors.orange.shade700;
      } else if (firstLockedVideo.weeksUntilUnlock != null) {
        weekStatus = 'W${firstLockedVideo.weeksUntilUnlock}';
        weekStatusColor = Colors.grey;
      }

      weekSections.add(
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Week header with status
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 2,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Week $weekNumber',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  if (weekStatus.isNotEmpty)
                    Flexible(
                      child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: weekStatusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: weekStatusColor),
                      ),
                      child: Text(
                        weekStatus,
                        style: TextStyle(
                          fontSize: 12,
                          color: weekStatusColor,
                          fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Week videos
            ...weekVideos.map((video) => _buildVideoCard(video)).toList(),

            const SizedBox(height: 16),
          ],
        ),
      );
    }

    return weekSections;
  }

  Widget _buildVideoThumbnail(CourseVideo video, String primaryThumbnailUrl) {
    return _ThumbnailWithFallback(
      video: video,
      primaryThumbnailUrl: primaryThumbnailUrl,
      width: 100,
      height: 60,
    );
  }

  Widget _buildVideoCard(CourseVideo video) {
    final bool isLocked = !video.isUnlocked;
    final thumbnailUrl = (video.thumbnailUrl != null && video.thumbnailUrl!.isNotEmpty)
        ? video.thumbnailUrl!
        : video.getVimeoThumbnailUrl();

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: isLowEndDevice ? 0 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: isLocked ? () {
          _showUnlockInfoDialog(video);
        } : () {
          _openDedicatedVideoPlayer(video);
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Video thumbnail or placeholder
              Stack(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: _buildVideoThumbnail(video, thumbnailUrl),
                  ),

                  // Lock overlay for locked videos
                  if (isLocked)
                    Container(
                      width: 100,
                      height: 60,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: isLowEndDevice ? null : [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 2,
                            offset: Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              video.isCurrentWeek ? Icons.hourglass_top : Icons.lock,
                              color: Theme.of(context).colorScheme.onSurface,
                              size: 20,
                            ),
                            if (video.weeksUntilUnlock != null)
                              Text(
                                'Week ${video.weekNumber}',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),

                  // Play button for unlocked videos
                  if (!isLocked)
                    Positioned.fill(
                      child: Center(
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            shape: BoxShape.circle,
                            boxShadow: isLowEndDevice ? null : [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05),
                                blurRadius: 2,
                                offset: Offset(0, 1),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.play_arrow,
                            color: Theme.of(context).colorScheme.onSurface,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(width: 12),

              // Video details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      video.title,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: isLocked ? KFTDesignSystem.getTextSecondaryColor(context) : Theme.of(context).colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    
                    // Video metadata row
                    Row(
                      children: [
                        // Duration
                        if (video.durationMinutes != null) ...[
                              Icon(
                                Icons.access_time,
                                size: 14,
                                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${video.durationMinutes} min',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                                ),
                              ),
                        ],
                        
                        const Spacer(),

                        // Status badge
                        _buildVideoBadge(video),
                      ],
                    ),

                    // Unlock date for locked videos
                    if (isLocked && video.unlockDate != null)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Text(
                          'Unlocks on: ${_formatDate(video.unlockDate!)}',
                          style: TextStyle(
                            fontSize: 11,
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),

                    // Progress bar for started videos
                    if (!isLocked && video.watchDurationSeconds != null && video.watchDurationSeconds! > 0 && !video.isCompleted)
                      Consumer<ProgressService>(
                        builder: (context, progressService, child) {
                          final progress = progressService.getVideoProgress(video.id);
                          final watchedSeconds = progress?.watchDurationSeconds ?? video.watchDurationSeconds ?? 0;
                          final durationSeconds = video.durationMinutes != null ? video.durationMinutes! * 60 : 1;
                          return Padding(
                            padding: const EdgeInsets.only(top: 4),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(2),
                              child: LinearProgressIndicator(
                                value: durationSeconds > 0 ? (watchedSeconds / durationSeconds).clamp(0.0, 1.0) : 0.5,
                                minHeight: 4,
                                backgroundColor: Theme.of(context).colorScheme.surface.withOpacity(0.1),
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper method to format dates
  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  // Show dialog with unlock information
  void _showUnlockInfoDialog(CourseVideo video) {
    String message = 'This video is currently locked.';
    String title = 'Video Locked';

    if (video.unlockDate != null) {
      message = 'This video will be unlocked on ${_formatDate(video.unlockDate!)}.';

      if (video.isCurrentWeek) {
        title = 'Unlocking Soon';
        message = 'This video will be unlocked very soon! Check back in a day or two.';
      } else if (video.weeksUntilUnlock != null) {
        if (video.weeksUntilUnlock == 1) {
          title = 'Unlocks Next Week';
          message = 'This video will be unlocked next week. It\'s part of your Week ${video.weekNumber} content.';
        } else {
          title = 'Unlocks in Week ${video.weekNumber}';
          message = 'This video will be unlocked in ${video.weeksUntilUnlock} weeks. It\'s part of your Week ${video.weekNumber} content.';
        }
      }
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(message),
            SizedBox(height: 16),
            Text(
              'Videos are unlocked weekly based on your course start date. This helps you progress through the course at a steady pace.',
              style: TextStyle(
                fontSize: 12,
                fontStyle: FontStyle.italic,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildVideoBadge(CourseVideo video) {
    if (video.isCompleted) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Theme.of(context).colorScheme.secondary),
        ),
        child: Text(
          'Done',
              style: TextStyle(
                fontSize: 10,
                color: Theme.of(context).colorScheme.secondary,
                fontWeight: FontWeight.bold,
              ),
        ),
      );
    } else if (!video.isUnlocked) {
      // Show when the video will unlock
      String unlockText = 'Locked';
      Color badgeColor = KFTDesignSystem.getTextSecondaryColor(context);

      if (video.isCurrentWeek) {
        unlockText = 'Soon';
        badgeColor = Colors.orange;
      } else if (video.weeksUntilUnlock == 1) {
        unlockText = 'Next';
          badgeColor = Colors.orange.shade700;
      } else if (video.weeksUntilUnlock != null) {
        unlockText = 'W${video.weeksUntilUnlock}';
          badgeColor = Colors.grey;
      }

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: badgeColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: badgeColor),
        ),
        child: Text(
              unlockText,
              style: TextStyle(
                fontSize: 10,
                color: badgeColor,
                fontWeight: FontWeight.bold,
              ),
            ),
      );
    } else {
      // Video is unlocked but not completed
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Theme.of(context).colorScheme.primary),
        ),
        child: Text(
          'Ready',
              style: TextStyle(
                fontSize: 10,
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
        ),
      );
    }
  }

  void _openDedicatedVideoPlayer(CourseVideo video) async {
    try {
      // Get user profile for watermark
      final userProfile = await _getUserProfile();

      // Create Course object from course data
      Course? course;
      if (_courseData != null) {
        course = Course(
          id: widget.courseId,
          title: _courseData!['title']?.toString() ?? 'Course Videos',
          description: _courseData!['description']?.toString() ?? '',
          thumbnailUrl: _courseData!['thumbnail_url']?.toString(),
          durationWeeks: _courseData!['duration_weeks'] is int
            ? _courseData!['duration_weeks']
            : int.tryParse(_courseData!['duration_weeks']?.toString() ?? '1') ?? 1,
          startDate: DateTime.now().toString(),
          status: 'active',
          totalVideos: _courseData!['total_videos'] is int
            ? _courseData!['total_videos']
            : int.tryParse(_courseData!['total_videos']?.toString() ?? '0') ?? 0,
          unlockedVideos: _courseData!['total_videos'] is int
            ? _courseData!['total_videos']
            : int.tryParse(_courseData!['total_videos']?.toString() ?? '0') ?? 0,
          completedVideos: _courseData!['completed_videos'] is int
            ? _courseData!['completed_videos']
            : int.tryParse(_courseData!['completed_videos']?.toString() ?? '0') ?? 0,
          progressPercentage: _courseData!['progress_percentage'] is int
            ? _courseData!['progress_percentage']
            : int.tryParse(_courseData!['progress_percentage']?.toString() ?? '0') ?? 0,
          instructor: 'KFT Instructor',
          level: 'beginner',
        );
      }

      // Navigate to comprehensive video player page
      await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ComprehensiveVideoPlayerPage(
            initialVideo: video,
            course: course,
            userProfile: userProfile,
          ),
        ),
      ).then((_) => _loadCourseVideos()); // Force UI update on return
    } catch (e) {
      debugPrint('Failed to open comprehensive video player: $e');
      // Fallback to showing error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to open video player: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<UserProfile?> _getUserProfile() async {
    try {
      final response = await _apiService.makeApiRequest('profile.php');
      if (response['success'] == true && response['profile'] != null) {
        return UserProfile.fromJson(response['profile']);
      }
      return null;
    } catch (e) {
      debugPrint('Failed to get user profile: $e');
      return null;
    }
  }
}

/// A widget that displays a video thumbnail with multiple fallback options
class _ThumbnailWithFallback extends StatefulWidget {
  final CourseVideo video;
  final String primaryThumbnailUrl;
  final double width;
  final double height;

  const _ThumbnailWithFallback({
    Key? key,
    required this.video,
    required this.primaryThumbnailUrl,
    required this.width,
    required this.height,
  }) : super(key: key);

  @override
  _ThumbnailWithFallbackState createState() => _ThumbnailWithFallbackState();
}

class _ThumbnailWithFallbackState extends State<_ThumbnailWithFallback> {
  int _currentFallbackIndex = 0;
  bool _hasError = false;

  List<String> get _fallbackUrls {
    final urls = <String>[];

    // Primary thumbnail URL (could be custom thumbnail or already a Vimeo URL)
    if (widget.primaryThumbnailUrl.isNotEmpty) {
      urls.add(widget.primaryThumbnailUrl);
    }

    // Vimeo official CDN thumbnail as fallback
    final vimeoThumbnail = widget.video.getVimeoThumbnailUrl();
    if (vimeoThumbnail.isNotEmpty && !urls.contains(vimeoThumbnail)) {
      urls.add(vimeoThumbnail);
    }

    return urls;
  }

  @override
  Widget build(BuildContext context) {
    final urls = _fallbackUrls;

    // If no URLs available, show placeholder
    if (urls.isEmpty || _currentFallbackIndex >= urls.length) {
      return _buildPlaceholder();
    }

    return Image.network(
      urls[_currentFallbackIndex],
      width: widget.width,
      height: widget.height,
      fit: BoxFit.cover,
      cacheWidth: (widget.width * 2).round(),
      cacheHeight: (widget.height * 2).round(),
      frameBuilder: (context, child, frame, wasSynchronouslyLoaded) {
        if (wasSynchronouslyLoaded) return child;
        return AnimatedOpacity(
          opacity: frame == null ? 0 : 1,
          duration: const Duration(milliseconds: 300),
          child: child,
        );
      },
      errorBuilder: (context, error, stackTrace) {
        // Try next fallback URL
        if (_currentFallbackIndex < urls.length - 1) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _currentFallbackIndex++;
              });
            }
          });
          return _buildPlaceholder(); // Show placeholder while switching
        }

        // All fallbacks failed, show final placeholder
        return _buildPlaceholder();
      },
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      width: widget.width,
      height: widget.height,
      color: KFTDesignSystem.getCardColor(context),
      child: Icon(
        Icons.video_library,
        color: KFTDesignSystem.getTextSecondaryColor(context),
        size: widget.width * 0.3,
      ),
    );
  }
}