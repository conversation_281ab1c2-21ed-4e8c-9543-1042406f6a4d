/// Utility class for phone number validation and formatting
/// Provides flexible phone number handling with or without country codes
class PhoneUtils {
  // Common country codes and their patterns
  static const Map<String, String> _countryCodes = {
    '+91': 'India',
    '+1': 'US/Canada',
    '+44': 'UK',
    '+86': 'China',
    '+81': 'Japan',
    '+49': 'Germany',
    '+33': 'France',
    '+39': 'Italy',
    '+34': 'Spain',
    '+7': 'Russia',
  };

  /// Normalize phone number by removing all non-digit characters except +
  static String normalizePhoneNumber(String phone) {
    if (phone.isEmpty) return '';
    
    // Keep only digits and + at the beginning
    String normalized = phone.replaceAll(RegExp(r'[^\d\+]'), '');
    
    // Ensure + is only at the beginning
    if (normalized.contains('+')) {
      List<String> parts = normalized.split('+');
      normalized = '+' + parts.where((part) => part.isNotEmpty).join('');
    }
    
    return normalized;
  }

  /// Get all possible variations of a phone number for backend matching
  static List<String> getPhoneNumberVariations(String phone) {
    String normalized = normalizePhoneNumber(phone);
    List<String> variations = [];
    
    if (normalized.isEmpty) return variations;
    
    // Add the normalized version
    variations.add(normalized);
    
    // Remove + if present for digit-only version
    String digitsOnly = normalized.replaceAll('+', '');
    if (digitsOnly != normalized) {
      variations.add(digitsOnly);
    }
    
    // If it's 10 digits, add with +91 prefix (for Indian numbers)
    if (digitsOnly.length == 10) {
      variations.add('91$digitsOnly');
      variations.add('+91$digitsOnly');
    }
    
    // If it starts with 91 and has 12 digits, also add without country code
    if (digitsOnly.length == 12 && digitsOnly.startsWith('91')) {
      String withoutCountryCode = digitsOnly.substring(2);
      variations.add(withoutCountryCode);
      variations.add('+$digitsOnly');
    }
    
    // If it starts with 1 and has 11 digits, also add without country code
    if (digitsOnly.length == 11 && digitsOnly.startsWith('1')) {
      String withoutCountryCode = digitsOnly.substring(1);
      variations.add(withoutCountryCode);
      variations.add('+$digitsOnly');
    }
    
    // Add original input if different
    if (phone != normalized && phone.isNotEmpty) {
      variations.add(phone);
    }
    
    // Remove duplicates
    return variations.toSet().toList();
  }

  /// Validate phone number with flexible rules
  static PhoneValidationResult validatePhoneNumber(String phone) {
    if (phone.isEmpty) {
      return PhoneValidationResult(
        isValid: false,
        errorMessage: 'Phone number is required',
      );
    }
    
    String normalized = normalizePhoneNumber(phone);
    String digitsOnly = normalized.replaceAll('+', '');
    
    // Check minimum length (at least 7 digits for any valid phone)
    if (digitsOnly.length < 7) {
      return PhoneValidationResult(
        isValid: false,
        errorMessage: 'Phone number is too short',
      );
    }
    
    // Check maximum length (15 digits is international standard)
    if (digitsOnly.length > 15) {
      return PhoneValidationResult(
        isValid: false,
        errorMessage: 'Phone number is too long',
      );
    }
    
    // Check for valid patterns
    if (normalized.startsWith('+')) {
      // International format validation
      String countryCode = _extractCountryCode(normalized);
      if (countryCode.isEmpty) {
        return PhoneValidationResult(
          isValid: false,
          errorMessage: 'Invalid country code format',
        );
      }
    }
    
    // All checks passed
    return PhoneValidationResult(
      isValid: true,
      normalizedPhone: normalized,
      variations: getPhoneNumberVariations(phone),
    );
  }

  /// Extract country code from phone number
  static String _extractCountryCode(String phone) {
    if (!phone.startsWith('+')) return '';
    
    // Try to match known country codes
    for (String code in _countryCodes.keys) {
      if (phone.startsWith(code)) {
        return code;
      }
    }
    
    // For unknown codes, extract up to 4 digits after +
    RegExp codePattern = RegExp(r'^\+(\d{1,4})');
    Match? match = codePattern.firstMatch(phone);
    return match != null ? '+${match.group(1)}' : '';
  }

  /// Format phone number for display
  static String formatPhoneForDisplay(String phone) {
    String normalized = normalizePhoneNumber(phone);
    if (normalized.isEmpty) return phone;
    
    // If it starts with +91, format as +91 XXXXX XXXXX
    if (normalized.startsWith('+91') && normalized.length == 13) {
      return '+91 ${normalized.substring(3, 8)} ${normalized.substring(8)}';
    }
    
    // If it's 10 digits, format as XXXXX XXXXX
    String digitsOnly = normalized.replaceAll('+', '');
    if (digitsOnly.length == 10) {
      return '${digitsOnly.substring(0, 5)} ${digitsOnly.substring(5)}';
    }
    
    // For other formats, return normalized
    return normalized;
  }

  /// Check if phone number looks like it needs a country code
  static bool needsCountryCode(String phone) {
    String digitsOnly = normalizePhoneNumber(phone).replaceAll('+', '');
    
    // If exactly 10 digits and doesn't start with country code, might need +91
    return digitsOnly.length == 10 && !phone.startsWith('+');
  }

  /// Get suggested format for user input
  static String getSuggestedFormat(String phone) {
    if (needsCountryCode(phone)) {
      return '+91$phone';
    }
    return normalizePhoneNumber(phone);
  }

  /// Get user-friendly error message for phone validation
  static String getValidationHint(String phone) {
    if (phone.isEmpty) {
      return 'Enter your phone number (e.g., +91XXXXXXXXXX or XXXXXXXXXX)';
    }
    
    String normalized = normalizePhoneNumber(phone);
    String digitsOnly = normalized.replaceAll('+', '');
    
    if (digitsOnly.length < 7) {
      return 'Phone number is too short. Enter at least 7 digits.';
    }
    
    if (digitsOnly.length > 15) {
      return 'Phone number is too long. Maximum 15 digits allowed.';
    }
    
    if (digitsOnly.length == 10) {
      return 'Valid format. You can also add country code like +91.';
    }
    
    return 'Enter a valid phone number';
  }
}

/// Result class for phone validation
class PhoneValidationResult {
  final bool isValid;
  final String? errorMessage;
  final String? normalizedPhone;
  final List<String>? variations;

  PhoneValidationResult({
    required this.isValid,
    this.errorMessage,
    this.normalizedPhone,
    this.variations,
  });
}
