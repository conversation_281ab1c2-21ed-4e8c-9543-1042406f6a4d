<!DOCTYPE html><html><head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="/">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Professional fitness training app with personalized workouts, nutrition guidance, and progress tracking. Your pocket-sized personal trainer.">

  <!-- PWA Meta Tags -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="default">
  <meta name="apple-mobile-web-app-title" content="KFT Fitness">
  <meta name="application-name" content="KFT Fitness">
  <meta name="theme-color" content="#3D5AFE">
  <meta name="msapplication-TileColor" content="#3D5AFE">

  <!-- Icons -->
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" sizes="192x192" href="icons/Icon-192.png">
  <link rel="icon" type="image/png" href="favicon.png">

  <title>KFT Fitness - Personal Training App</title>
  <link rel="manifest" href="manifest.json">

  <!-- Critical Resource Preloading for Lightning-Fast Performance -->
  <!-- Preload essential Flutter files -->
  <link rel="preload" href="main.dart.js" as="script" crossorigin>
  <link rel="preload" href="flutter.js" as="script" crossorigin>
  <link rel="preload" href="flutter_bootstrap.js" as="script" crossorigin>

  <!-- Preload critical fonts -->
  <link rel="preload" href="assets/fonts/MaterialIcons-Regular.otf" as="font" type="font/otf" crossorigin>
  <link rel="preload" href="assets/packages/cupertino_icons/assets/CupertinoIcons.ttf" as="font" type="font/ttf" crossorigin>

  <!-- Preload essential assets -->
  <link rel="preload" href="assets/AssetManifest.json" as="fetch" crossorigin>
  <link rel="preload" href="assets/FontManifest.json" as="fetch" crossorigin>
  <link rel="preload" href="assets/assets/images/logo.webp" as="image" crossorigin>

  <!-- Preload icons for instant display -->
  <link rel="preload" href="icons/Icon-192.png" as="image">
  <link rel="preload" href="icons/Icon-512.png" as="image">
  <link rel="preload" href="icons/Icon-maskable-192.png" as="image">
  <link rel="preload" href="icons/Icon-maskable-512.png" as="image">

  <!-- Preload splash screens -->
  <link rel="preload" href="splash/img/light-2x.png" as="image">
  <link rel="preload" href="splash/img/dark-2x.png" as="image">

  <!-- DNS prefetch for external resources -->
  <link rel="dns-prefetch" href="//player.vimeo.com">
  <link rel="dns-prefetch" href="//vimeo.com">
  <link rel="dns-prefetch" href="//fonts.googleapis.com">
  <link rel="dns-prefetch" href="//fonts.gstatic.com">
  <!-- Inline critical CSS here for above-the-fold content -->
  <style>
    html, body { height: 100%; margin: 0; background: #fff; }
    /* Add any other critical CSS for layout here */
  </style>
  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
</head>
<body>
  <div id="custom-splash">
    <div class="spinner"></div>
  </div>
  <script>
    window.addEventListener('beforeinstallprompt', function(e) {
      e.preventDefault();
      // Do not show any install prompt or banner
      return false;
    });
  </script>
  <style>
    #custom-splash {
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      width: 100vw; height: 100vh;
      background: #3D5AFE;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
    }
    .spinner {
      width: 60px;
      height: 60px;
      border: 8px solid #fff;
      border-top: 8px solid #3D5AFE;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
  <script src="flutter_bootstrap.js" async=""></script>

  <!-- PWA Service Worker Registration -->
  <script>
    // Register service worker for PWA functionality
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then((registration) => {
            console.log('✅ Service Worker registered successfully:', registration.scope);

            // Check for updates
            registration.addEventListener('updatefound', () => {
              console.log('🔄 Service Worker update found');
            });
          })
          .catch((error) => {
            console.error('❌ Service Worker registration failed:', error);
          });
      });
    } else {
      console.warn('⚠️ Service Worker not supported in this browser');
    }
  </script>

</body></html>